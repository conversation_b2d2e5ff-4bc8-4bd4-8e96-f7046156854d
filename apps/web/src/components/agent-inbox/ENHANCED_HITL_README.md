# Enhanced Human-in-the-Loop System

This enhanced human-in-the-loop (HITL) system provides a comprehensive visual interface for managing LangGraph workflow interrupts, state modifications, and real-time monitoring. It's built on top of the existing agent inbox infrastructure and provides advanced features for workflow visualization and interaction.

## Features

### 🔄 Visual Workflow Diagram
- **Interactive Node Visualization**: See your LangGraph workflow as an interactive diagram
- **Real-time State Updates**: Nodes update their status as the workflow progresses
- **Execution Path Tracking**: Visual indication of the current execution path
- **Node Click Interactions**: Click on nodes to inspect their state or trigger actions

### 📝 Interactive State Editor
- **Direct State Modification**: Edit workflow state values directly in the UI
- **Type-aware Editing**: Different input types for strings, numbers, objects, and arrays
- **Change Tracking**: Visual diff showing what has been modified
- **Validation**: Real-time validation of state changes
- **Undo/Redo**: Full history of state modifications

### 🚨 Enhanced Interrupt Management
- **Priority-based Organization**: Categorize interrupts by priority (critical, high, medium, low)
- **Category Filtering**: Group interrupts by type (approval, input, decision, review, error)
- **Batch Operations**: Handle multiple interrupts efficiently
- **Time Tracking**: See how long interrupts have been pending
- **Assignment**: Assign interrupts to specific team members

### 📡 Real-time Updates
- **WebSocket Integration**: Live updates from the workflow execution
- **Event Streaming**: Real-time notifications for state changes, interrupts, and completions
- **Connection Status**: Visual indication of connection health
- **Notification System**: Configurable notifications for important events

## Components

### `EnhancedHumanInTheLoop`
Main component that orchestrates all enhanced HITL features.

```typescript
import { EnhancedHumanInTheLoop } from './enhanced-human-in-the-loop';

<EnhancedHumanInTheLoop
  threadData={threadData}
  onThreadUpdate={handleThreadUpdate}
  onWorkflowAction={handleWorkflowAction}
/>
```

### `WorkflowDiagram`
Interactive workflow visualization component.

```typescript
import { WorkflowDiagram } from './components/workflow-diagram';

<WorkflowDiagram
  graph={workflowGraph}
  onNodeClick={handleNodeClick}
  interactive={true}
/>
```

### `InteractiveStateEditor`
State editing component with diff tracking.

```typescript
import { InteractiveStateEditor } from './components/interactive-state-editor';

<InteractiveStateEditor
  state={threadState}
  onStateChange={handleStateChange}
  readOnly={false}
  showDiff={true}
/>
```

### `EnhancedInterruptManager`
Advanced interrupt management with filtering and prioritization.

```typescript
import { EnhancedInterruptManager } from './components/enhanced-interrupt-manager';

<EnhancedInterruptManager
  interrupts={enhancedInterrupts}
  onInterruptAction={handleInterruptAction}
  onInterruptPriorityChange={handlePriorityChange}
  showFilters={true}
/>
```

### `RealTimeUpdates`
Real-time monitoring and updates component.

```typescript
import { RealTimeUpdates } from './components/real-time-updates';

<RealTimeUpdates
  threadId={threadId}
  onUpdate={handleRealTimeUpdate}
  autoConnect={true}
  showNotifications={true}
/>
```

## Integration with LangGraph

### Setting up Interrupts
The enhanced system works with standard LangGraph interrupts but adds additional metadata:

```python
from langgraph.types import interrupt

# Standard LangGraph interrupt
user_input = interrupt("Please provide feedback:")

# Enhanced interrupt with metadata (handled by the UI)
# The UI will automatically enhance interrupts with:
# - Priority levels
# - Categories
# - Timestamps
# - Assignment capabilities
```

### State Management
The system can modify LangGraph state through the interactive editor:

```typescript
// Handle state modifications
const handleStateChange = (modifications: StateModification[]) => {
  // Apply modifications to LangGraph state
  modifications.forEach(mod => {
    // Update state at mod.path with mod.newValue
    updateLangGraphState(mod.path, mod.newValue);
  });
};
```

### Real-time Integration
Connect to your LangGraph execution for real-time updates:

```typescript
// WebSocket connection to LangGraph execution
const ws = new WebSocket('ws://your-langgraph-server/stream');
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  // Forward to RealTimeUpdates component
  handleRealTimeUpdate(update);
};
```

## Usage Examples

### Basic Integration
```typescript
import { ThreadView } from './thread-view';

// The enhanced view is automatically available in ThreadView
// Users can toggle between classic and enhanced views
<ThreadView threadId={threadId} />
```

### Custom Workflow Actions
```typescript
const handleWorkflowAction = (action: 'play' | 'pause' | 'stop' | 'restart') => {
  switch (action) {
    case 'play':
      // Resume LangGraph execution
      langGraphClient.resume(threadId);
      break;
    case 'pause':
      // Pause execution
      langGraphClient.pause(threadId);
      break;
    case 'stop':
      // Stop execution
      langGraphClient.stop(threadId);
      break;
    case 'restart':
      // Restart from beginning
      langGraphClient.restart(threadId);
      break;
  }
};
```

### Interrupt Handling
```typescript
const handleInterruptAction = (interruptId: string, action: HumanResponse) => {
  // Send response back to LangGraph
  langGraphClient.sendHumanResponse(threadId, [action]);
  
  // Update local state
  updateThreadData(threadId);
};
```

## Customization

### Styling
All components use Tailwind CSS and can be customized through className props:

```typescript
<EnhancedHumanInTheLoop
  className="custom-hitl-styles"
  threadData={threadData}
/>
```

### Workflow Graph Layout
Customize the workflow diagram layout:

```typescript
const customGraph: WorkflowGraph = {
  nodes: [
    {
      id: 'start',
      name: 'Start Process',
      type: 'start',
      status: 'completed',
      position: { x: 100, y: 100 },
      metadata: { description: 'Initial step' }
    }
    // ... more nodes
  ],
  edges: [
    {
      id: 'e1',
      source: 'start',
      target: 'process',
      label: 'begin'
    }
    // ... more edges
  ],
  currentNode: 'process',
  executionPath: ['start', 'process']
};
```

### Real-time Configuration
Configure real-time updates:

```typescript
<RealTimeUpdates
  threadId={threadId}
  onUpdate={handleUpdate}
  autoConnect={true}
  showNotifications={true}
  // Custom WebSocket URL
  wsUrl="ws://custom-langgraph-server/stream"
/>
```

## Best Practices

1. **State Modifications**: Always validate state changes before applying them to prevent workflow corruption
2. **Interrupt Prioritization**: Use priority levels to ensure critical interrupts are handled first
3. **Real-time Performance**: Consider throttling real-time updates for high-frequency workflows
4. **Error Handling**: Implement proper error boundaries around enhanced components
5. **Accessibility**: Ensure keyboard navigation works for all interactive elements

## Troubleshooting

### Common Issues

**Workflow diagram not showing**: Ensure your WorkflowGraph data structure is properly formatted with valid node positions.

**Real-time updates not working**: Check WebSocket connection and ensure your LangGraph server supports streaming.

**State modifications not persisting**: Verify that state change handlers are properly connected to your LangGraph backend.

**Performance issues**: Consider implementing virtualization for large workflows or many interrupts.

## Future Enhancements

- **Collaborative Editing**: Multiple users editing state simultaneously
- **Workflow Templates**: Pre-built workflow patterns
- **Advanced Analytics**: Workflow performance metrics and insights
- **Mobile Support**: Responsive design for mobile devices
- **Plugin System**: Extensible architecture for custom components
