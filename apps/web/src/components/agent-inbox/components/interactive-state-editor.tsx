import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Edit3, 
  Save, 
  X, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { StateModification } from '../types';
import { toast } from 'sonner';

interface InteractiveStateEditorProps {
  state: Record<string, any>;
  onStateChange: (modifications: StateModification[]) => void;
  readOnly?: boolean;
  className?: string;
  showDiff?: boolean;
}

interface EditingField {
  path: string[];
  originalValue: any;
  currentValue: any;
  isValid: boolean;
  error?: string;
}

const getValueType = (value: any): string => {
  if (value === null) return 'null';
  if (Array.isArray(value)) return 'array';
  return typeof value;
};

const formatValue = (value: any): string => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;
  return JSON.stringify(value, null, 2);
};

const parseValue = (stringValue: string, originalType: string): any => {
  if (stringValue === '' && originalType === 'null') return null;
  if (originalType === 'string') return stringValue;
  if (originalType === 'number') {
    const num = Number(stringValue);
    if (isNaN(num)) throw new Error('Invalid number');
    return num;
  }
  if (originalType === 'boolean') {
    if (stringValue.toLowerCase() === 'true') return true;
    if (stringValue.toLowerCase() === 'false') return false;
    throw new Error('Invalid boolean');
  }
  try {
    return JSON.parse(stringValue);
  } catch {
    throw new Error('Invalid JSON');
  }
};

const StateFieldEditor: React.FC<{
  path: string[];
  value: any;
  onEdit: (path: string[], newValue: any) => void;
  onCancel: (path: string[]) => void;
  editing: EditingField | null;
  readOnly: boolean;
}> = ({ path, value, onEdit, onCancel, editing, readOnly }) => {
  const pathString = path.join('.');
  const isEditing = editing?.path.join('.') === pathString;
  const valueType = getValueType(value);
  const displayValue = formatValue(value);
  
  const [editValue, setEditValue] = useState(displayValue);
  const [error, setError] = useState<string>('');

  React.useEffect(() => {
    if (isEditing) {
      setEditValue(formatValue(editing.originalValue));
      setError('');
    }
  }, [isEditing, editing]);

  const handleSave = useCallback(() => {
    try {
      const parsedValue = parseValue(editValue, valueType);
      onEdit(path, parsedValue);
      setError('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Invalid value');
    }
  }, [editValue, valueType, path, onEdit]);

  const handleCancel = useCallback(() => {
    onCancel(path);
    setError('');
  }, [path, onCancel]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [handleSave, handleCancel]);

  if (isEditing) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md"
      >
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-xs">
            {valueType}
          </Badge>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              disabled={!!error}
              className="h-6 w-6 p-0"
            >
              <Save className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        {valueType === 'string' ? (
          <Textarea
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="min-h-[60px] text-sm"
            placeholder="Enter value..."
          />
        ) : (
          <Input
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="text-sm"
            placeholder="Enter value..."
          />
        )}
        
        {error && (
          <div className="flex items-center gap-1 text-red-600 text-xs">
            <AlertTriangle className="h-3 w-3" />
            {error}
          </div>
        )}
        
        <div className="text-xs text-gray-500">
          Press Ctrl+Enter to save, Esc to cancel
        </div>
      </motion.div>
    );
  }

  return (
    <div className="group flex items-center justify-between p-2 hover:bg-gray-50 rounded-md">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {valueType}
          </Badge>
          <span className="text-sm font-mono truncate">
            {displayValue || <em className="text-gray-400">empty</em>}
          </span>
        </div>
      </div>
      
      {!readOnly && (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onEdit(path, value)}
          className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 transition-opacity"
        >
          <Edit3 className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export const InteractiveStateEditor: React.FC<InteractiveStateEditorProps> = ({
  state,
  onStateChange,
  readOnly = false,
  className,
  showDiff = true
}) => {
  const [modifications, setModifications] = useState<StateModification[]>([]);
  const [editing, setEditing] = useState<EditingField | null>(null);
  const [showModifications, setShowModifications] = useState(true);

  const modifiedState = useMemo(() => {
    let result = { ...state };
    modifications.forEach(mod => {
      let current = result;
      for (let i = 0; i < mod.path.length - 1; i++) {
        current = current[mod.path[i]];
      }
      current[mod.path[mod.path.length - 1]] = mod.newValue;
    });
    return result;
  }, [state, modifications]);

  const handleEdit = useCallback((path: string[], value: any) => {
    if (editing) return; // Already editing something
    
    setEditing({
      path,
      originalValue: value,
      currentValue: value,
      isValid: true
    });
  }, [editing]);

  const handleSave = useCallback((path: string[], newValue: any) => {
    const pathString = path.join('.');
    const existingModIndex = modifications.findIndex(
      mod => mod.path.join('.') === pathString
    );

    const modification: StateModification = {
      path,
      oldValue: editing?.originalValue,
      newValue,
      timestamp: new Date()
    };

    let newModifications;
    if (existingModIndex >= 0) {
      newModifications = [...modifications];
      newModifications[existingModIndex] = modification;
    } else {
      newModifications = [...modifications, modification];
    }

    setModifications(newModifications);
    setEditing(null);
    onStateChange(newModifications);
    
    toast.success('State updated', {
      description: `Modified ${path.join('.')}`
    });
  }, [modifications, editing, onStateChange]);

  const handleCancel = useCallback(() => {
    setEditing(null);
  }, []);

  const handleUndo = useCallback(() => {
    if (modifications.length > 0) {
      const newModifications = modifications.slice(0, -1);
      setModifications(newModifications);
      onStateChange(newModifications);
      toast.info('Change undone');
    }
  }, [modifications, onStateChange]);

  const handleReset = useCallback(() => {
    setModifications([]);
    setEditing(null);
    onStateChange([]);
    toast.info('All changes reset');
  }, [onStateChange]);

  const renderStateObject = (obj: any, path: string[] = []): React.ReactNode => {
    if (obj === null || obj === undefined) {
      return (
        <StateFieldEditor
          key={path.join('.')}
          path={path}
          value={obj}
          onEdit={handleEdit}
          onCancel={handleCancel}
          editing={editing}
          readOnly={readOnly}
        />
      );
    }

    if (typeof obj !== 'object' || Array.isArray(obj)) {
      return (
        <StateFieldEditor
          key={path.join('.')}
          path={path}
          value={obj}
          onEdit={handleEdit}
          onCancel={handleCancel}
          editing={editing}
          readOnly={readOnly}
        />
      );
    }

    return (
      <div key={path.join('.')} className="space-y-1">
        {Object.entries(obj).map(([key, value]) => (
          <div key={key} className="ml-4">
            <div className="text-sm font-medium text-gray-700 mb-1">
              {key}
            </div>
            {renderStateObject(value, [...path, key])}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">State Editor</h3>
          {modifications.length > 0 && (
            <Badge variant="secondary">
              {modifications.length} change{modifications.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {showDiff && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowModifications(!showModifications)}
            >
              {showModifications ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          )}
          
          {!readOnly && modifications.length > 0 && (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleUndo}
                disabled={modifications.length === 0}
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleReset}
                disabled={modifications.length === 0}
              >
                Reset
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Modifications Summary */}
      <AnimatePresence>
        {showDiff && showModifications && modifications.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-yellow-50 border border-yellow-200 rounded-md p-3"
          >
            <h4 className="text-sm font-medium text-yellow-800 mb-2">
              Pending Changes
            </h4>
            <div className="space-y-1">
              {modifications.map((mod, index) => (
                <div key={index} className="text-xs text-yellow-700">
                  <span className="font-mono">{mod.path.join('.')}</span>
                  <span className="mx-2">→</span>
                  <span className="font-mono">
                    {JSON.stringify(mod.newValue)}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* State Tree */}
      <div className="border border-gray-200 rounded-md p-4 max-h-96 overflow-y-auto">
        {renderStateObject(modifiedState)}
      </div>
    </div>
  );
};
