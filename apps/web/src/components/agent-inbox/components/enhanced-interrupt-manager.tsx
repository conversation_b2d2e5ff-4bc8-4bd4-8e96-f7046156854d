import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertCircle, 
  Clock, 
  User, 
  CheckCircle, 
  XCircle,
  ArrowRight,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { HumanInterrupt, HumanResponse } from '../types';
import { format } from 'date-fns';

interface EnhancedInterrupt extends HumanInterrupt {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'approval' | 'input' | 'decision' | 'review' | 'error';
  timestamp: Date;
  estimatedTime?: number; // in minutes
  assignedTo?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
}

interface EnhancedInterruptManagerProps {
  interrupts: EnhancedInterrupt[];
  onInterruptAction: (interruptId: string, action: HumanResponse) => void;
  onInterruptPriorityChange?: (interruptId: string, priority: EnhancedInterrupt['priority']) => void;
  onInterruptAssign?: (interruptId: string, userId: string) => void;
  className?: string;
  showFilters?: boolean;
}

const getPriorityColor = (priority: EnhancedInterrupt['priority']) => {
  switch (priority) {
    case 'critical':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryIcon = (category: EnhancedInterrupt['category']) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (category) {
    case 'approval':
      return <CheckCircle {...iconProps} />;
    case 'input':
      return <User {...iconProps} />;
    case 'decision':
      return <AlertCircle {...iconProps} />;
    case 'review':
      return <Clock {...iconProps} />;
    case 'error':
      return <XCircle {...iconProps} />;
    default:
      return <AlertCircle {...iconProps} />;
  }
};

const getStatusColor = (status: EnhancedInterrupt['status']) => {
  switch (status) {
    case 'pending':
      return 'bg-gray-100 text-gray-800';
    case 'in_progress':
      return 'bg-blue-100 text-blue-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const InterruptCard: React.FC<{
  interrupt: EnhancedInterrupt;
  onAction: (action: HumanResponse) => void;
  onPriorityChange?: (priority: EnhancedInterrupt['priority']) => void;
  onAssign?: (userId: string) => void;
}> = ({ interrupt, onAction, onPriorityChange, onAssign }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleAccept = useCallback(() => {
    onAction({ type: 'accept', args: null });
  }, [onAction]);

  const handleReject = useCallback(() => {
    onAction({ type: 'ignore', args: null });
  }, [onAction]);

  const timeAgo = useMemo(() => {
    const now = new Date();
    const diff = now.getTime() - interrupt.timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }, [interrupt.timestamp]);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full"
    >
      <Card className={cn(
        "transition-all duration-200 hover:shadow-md",
        interrupt.priority === 'critical' && "border-red-300 shadow-red-100",
        interrupt.priority === 'high' && "border-orange-300 shadow-orange-100"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {getCategoryIcon(interrupt.category)}
                <CardTitle className="text-base">
                  {interrupt.action_request?.action || 'Interrupt'}
                </CardTitle>
              </div>
              <Badge className={getPriorityColor(interrupt.priority)}>
                {interrupt.priority}
              </Badge>
              <Badge variant="outline" className={getStatusColor(interrupt.status)}>
                {interrupt.status.replace('_', ' ')}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">{timeAgo}</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onPriorityChange && (
                    <>
                      <DropdownMenuItem onClick={() => onPriorityChange('critical')}>
                        Set Critical Priority
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onPriorityChange('high')}>
                        Set High Priority
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onPriorityChange('medium')}>
                        Set Medium Priority
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onPriorityChange('low')}>
                        Set Low Priority
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                    </>
                  )}
                  <DropdownMenuItem onClick={() => setIsExpanded(!isExpanded)}>
                    {isExpanded ? 'Collapse' : 'Expand'} Details
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {interrupt.description && (
            <p className="text-sm text-gray-600 mt-2">
              {interrupt.description}
            </p>
          )}
        </CardHeader>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {interrupt.action_request?.args && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Request Details</h4>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                          {JSON.stringify(interrupt.action_request.args, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Category:</span>
                      <span className="ml-2 capitalize">{interrupt.category}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Created:</span>
                      <span className="ml-2">
                        {format(interrupt.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    {interrupt.estimatedTime && (
                      <div>
                        <span className="font-medium text-gray-700">Est. Time:</span>
                        <span className="ml-2">{interrupt.estimatedTime}m</span>
                      </div>
                    )}
                    {interrupt.assignedTo && (
                      <div>
                        <span className="font-medium text-gray-700">Assigned:</span>
                        <span className="ml-2">{interrupt.assignedTo}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
        
        {interrupt.status === 'pending' && (
          <CardContent className="pt-0">
            <div className="flex items-center justify-end gap-2">
              {interrupt.config.allow_ignore && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReject}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              )}
              
              {interrupt.config.allow_accept && (
                <Button
                  size="sm"
                  onClick={handleAccept}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Accept
                </Button>
              )}
              
              {interrupt.config.allow_respond && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsExpanded(true)}
                >
                  <ArrowRight className="h-4 w-4 mr-1" />
                  Respond
                </Button>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </motion.div>
  );
};

export const EnhancedInterruptManager: React.FC<EnhancedInterruptManagerProps> = ({
  interrupts,
  onInterruptAction,
  onInterruptPriorityChange,
  onInterruptAssign,
  className,
  showFilters = true
}) => {
  const [sortBy, setSortBy] = useState<'priority' | 'timestamp' | 'category'>('priority');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<EnhancedInterrupt['status'] | 'all'>('all');
  const [filterPriority, setFilterPriority] = useState<EnhancedInterrupt['priority'] | 'all'>('all');

  const filteredAndSortedInterrupts = useMemo(() => {
    let filtered = interrupts;
    
    if (filterStatus !== 'all') {
      filtered = filtered.filter(interrupt => interrupt.status === filterStatus);
    }
    
    if (filterPriority !== 'all') {
      filtered = filtered.filter(interrupt => interrupt.priority === filterPriority);
    }
    
    return filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'timestamp':
          comparison = a.timestamp.getTime() - b.timestamp.getTime();
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [interrupts, sortBy, sortOrder, filterStatus, filterPriority]);

  const handleInterruptAction = useCallback((interruptId: string, action: HumanResponse) => {
    onInterruptAction(interruptId, action);
  }, [onInterruptAction]);

  const handlePriorityChange = useCallback((interruptId: string, priority: EnhancedInterrupt['priority']) => {
    if (onInterruptPriorityChange) {
      onInterruptPriorityChange(interruptId, priority);
    }
  }, [onInterruptPriorityChange]);

  const handleAssign = useCallback((interruptId: string, userId: string) => {
    if (onInterruptAssign) {
      onInterruptAssign(interruptId, userId);
    }
  }, [onInterruptAssign]);

  const priorityStats = useMemo(() => {
    const stats = { critical: 0, high: 0, medium: 0, low: 0 };
    interrupts.forEach(interrupt => {
      if (interrupt.status === 'pending') {
        stats[interrupt.priority]++;
      }
    });
    return stats;
  }, [interrupts]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold">Interrupts</h2>
          <div className="flex items-center gap-2">
            {priorityStats.critical > 0 && (
              <Badge className="bg-red-100 text-red-800">
                {priorityStats.critical} Critical
              </Badge>
            )}
            {priorityStats.high > 0 && (
              <Badge className="bg-orange-100 text-orange-800">
                {priorityStats.high} High
              </Badge>
            )}
          </div>
        </div>
        
        {showFilters && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-1" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setFilterStatus('all')}>
                  All Status
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterStatus('pending')}>
                  Pending Only
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setFilterPriority('all')}>
                  All Priorities
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterPriority('critical')}>
                  Critical Only
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterPriority('high')}>
                  High Only
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Interrupts List */}
      <div className="space-y-3">
        <AnimatePresence>
          {filteredAndSortedInterrupts.map(interrupt => (
            <InterruptCard
              key={interrupt.id}
              interrupt={interrupt}
              onAction={(action) => handleInterruptAction(interrupt.id, action)}
              onPriorityChange={(priority) => handlePriorityChange(interrupt.id, priority)}
              onAssign={(userId) => handleAssign(interrupt.id, userId)}
            />
          ))}
        </AnimatePresence>
        
        {filteredAndSortedInterrupts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No interrupts found</p>
          </div>
        )}
      </div>
    </div>
  );
};
