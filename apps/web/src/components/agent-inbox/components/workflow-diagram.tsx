import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Square, 
  Circle, 
  Diamond, 
  User, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { WorkflowGraph, WorkflowNode, WorkflowEdge } from '../types';

interface WorkflowDiagramProps {
  graph: WorkflowGraph;
  onNodeClick?: (nodeId: string) => void;
  className?: string;
  interactive?: boolean;
}

const NodeIcon = ({ type, status }: { type: WorkflowNode['type']; status: WorkflowNode['status'] }) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (type) {
    case 'start':
      return <Play {...iconProps} />;
    case 'end':
      return <Square {...iconProps} />;
    case 'process':
      return <Circle {...iconProps} />;
    case 'decision':
      return <Diamond {...iconProps} />;
    case 'human':
      return <User {...iconProps} />;
    default:
      return <Circle {...iconProps} />;
  }
};

const getNodeColor = (status: WorkflowNode['status']) => {
  switch (status) {
    case 'pending':
      return 'bg-gray-100 border-gray-300 text-gray-600';
    case 'active':
      return 'bg-blue-100 border-blue-400 text-blue-700 animate-pulse';
    case 'completed':
      return 'bg-green-100 border-green-400 text-green-700';
    case 'error':
      return 'bg-red-100 border-red-400 text-red-700';
    case 'interrupted':
      return 'bg-yellow-100 border-yellow-400 text-yellow-700';
    default:
      return 'bg-gray-100 border-gray-300 text-gray-600';
  }
};

const getStatusIcon = (status: WorkflowNode['status']) => {
  const iconProps = { className: "h-3 w-3" };
  
  switch (status) {
    case 'completed':
      return <CheckCircle {...iconProps} className="text-green-600" />;
    case 'error':
      return <XCircle {...iconProps} className="text-red-600" />;
    case 'interrupted':
      return <AlertCircle {...iconProps} className="text-yellow-600" />;
    case 'active':
      return <Clock {...iconProps} className="text-blue-600 animate-spin" />;
    default:
      return null;
  }
};

const WorkflowNodeComponent = ({ 
  node, 
  isInExecutionPath, 
  onClick, 
  interactive 
}: { 
  node: WorkflowNode; 
  isInExecutionPath: boolean;
  onClick?: (nodeId: string) => void;
  interactive?: boolean;
}) => {
  const handleClick = useCallback(() => {
    if (interactive && onClick) {
      onClick(node.id);
    }
  }, [interactive, onClick, node.id]);

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      whileHover={interactive ? { scale: 1.05 } : {}}
      className={cn(
        "absolute flex flex-col items-center justify-center",
        "min-w-[80px] min-h-[60px] p-3 rounded-lg border-2",
        "transition-all duration-200 ease-in-out",
        getNodeColor(node.status),
        interactive && "cursor-pointer hover:shadow-md",
        isInExecutionPath && "ring-2 ring-blue-300 ring-offset-2"
      )}
      style={{
        left: node.position.x,
        top: node.position.y,
      }}
      onClick={handleClick}
    >
      <div className="flex items-center gap-2 mb-1">
        <NodeIcon type={node.type} status={node.status} />
        {getStatusIcon(node.status)}
      </div>
      <span className="text-xs font-medium text-center leading-tight">
        {node.name}
      </span>
      {node.metadata?.description && (
        <span className="text-xs text-gray-500 text-center mt-1">
          {node.metadata.description}
        </span>
      )}
    </motion.div>
  );
};

const WorkflowEdgeComponent = ({ 
  edge, 
  nodes, 
  isInExecutionPath 
}: { 
  edge: WorkflowEdge; 
  nodes: WorkflowNode[];
  isInExecutionPath: boolean;
}) => {
  const sourceNode = nodes.find(n => n.id === edge.source);
  const targetNode = nodes.find(n => n.id === edge.target);
  
  if (!sourceNode || !targetNode) return null;

  const startX = sourceNode.position.x + 40; // Center of node
  const startY = sourceNode.position.y + 30;
  const endX = targetNode.position.x + 40;
  const endY = targetNode.position.y + 30;

  const midX = (startX + endX) / 2;
  const midY = (startY + endY) / 2;

  return (
    <g>
      <motion.line
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        x1={startX}
        y1={startY}
        x2={endX}
        y2={endY}
        stroke={isInExecutionPath ? "#3b82f6" : "#d1d5db"}
        strokeWidth={isInExecutionPath ? 3 : 2}
        strokeDasharray={isInExecutionPath ? "0" : "5,5"}
        markerEnd="url(#arrowhead)"
      />
      {edge.label && (
        <text
          x={midX}
          y={midY - 5}
          textAnchor="middle"
          className="text-xs fill-gray-600 font-medium"
        >
          {edge.label}
        </text>
      )}
    </g>
  );
};

export const WorkflowDiagram: React.FC<WorkflowDiagramProps> = ({
  graph,
  onNodeClick,
  className,
  interactive = true
}) => {
  const executionPathSet = useMemo(() => 
    new Set(graph.executionPath), 
    [graph.executionPath]
  );

  const svgDimensions = useMemo(() => {
    if (graph.nodes.length === 0) return { width: 400, height: 300 };
    
    const maxX = Math.max(...graph.nodes.map(n => n.position.x + 80));
    const maxY = Math.max(...graph.nodes.map(n => n.position.y + 60));
    
    return {
      width: Math.max(400, maxX + 50),
      height: Math.max(300, maxY + 50)
    };
  }, [graph.nodes]);

  return (
    <div className={cn("relative w-full h-full overflow-auto", className)}>
      <svg
        width={svgDimensions.width}
        height={svgDimensions.height}
        className="absolute inset-0"
      >
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#3b82f6"
            />
          </marker>
        </defs>
        
        {graph.edges.map(edge => (
          <WorkflowEdgeComponent
            key={edge.id}
            edge={edge}
            nodes={graph.nodes}
            isInExecutionPath={
              executionPathSet.has(edge.source) && 
              executionPathSet.has(edge.target)
            }
          />
        ))}
      </svg>
      
      <AnimatePresence>
        {graph.nodes.map(node => (
          <WorkflowNodeComponent
            key={node.id}
            node={node}
            isInExecutionPath={executionPathSet.has(node.id)}
            onClick={onNodeClick}
            interactive={interactive}
          />
        ))}
      </AnimatePresence>
      
      {graph.currentNode && (
        <motion.div
          className="absolute pointer-events-none"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          style={{
            left: graph.nodes.find(n => n.id === graph.currentNode)?.position.x || 0,
            top: (graph.nodes.find(n => n.id === graph.currentNode)?.position.y || 0) - 10,
          }}
        >
          <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
            Current
          </div>
        </motion.div>
      )}
    </div>
  );
};
