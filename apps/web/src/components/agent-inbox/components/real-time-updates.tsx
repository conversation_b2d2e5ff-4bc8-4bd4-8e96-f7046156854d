import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  Activity, 
  Bell, 
  BellOff,
  Pause,
  Play,
  Settings
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface RealTimeUpdate {
  id: string;
  type: 'state_change' | 'interrupt' | 'node_transition' | 'error' | 'completion';
  timestamp: Date;
  data: any;
  threadId?: string;
  nodeId?: string;
  message?: string;
}

interface RealTimeConnection {
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastUpdate?: Date;
  updateCount: number;
  latency?: number;
}

interface RealTimeUpdatesProps {
  threadId?: string;
  onUpdate?: (update: RealTimeUpdate) => void;
  onConnectionChange?: (connection: RealTimeConnection) => void;
  className?: string;
  autoConnect?: boolean;
  showNotifications?: boolean;
}

const getUpdateTypeColor = (type: RealTimeUpdate['type']) => {
  switch (type) {
    case 'state_change':
      return 'bg-blue-100 text-blue-800';
    case 'interrupt':
      return 'bg-yellow-100 text-yellow-800';
    case 'node_transition':
      return 'bg-green-100 text-green-800';
    case 'error':
      return 'bg-red-100 text-red-800';
    case 'completion':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getUpdateTypeIcon = (type: RealTimeUpdate['type']) => {
  const iconProps = { className: "h-3 w-3" };
  
  switch (type) {
    case 'state_change':
      return <Activity {...iconProps} />;
    case 'interrupt':
      return <Bell {...iconProps} />;
    case 'node_transition':
      return <Play {...iconProps} />;
    case 'error':
      return <WifiOff {...iconProps} />;
    case 'completion':
      return <Settings {...iconProps} />;
    default:
      return <Activity {...iconProps} />;
  }
};

const UpdateItem: React.FC<{ update: RealTimeUpdate }> = ({ update }) => {
  const timeAgo = React.useMemo(() => {
    const now = new Date();
    const diff = now.getTime() - update.timestamp.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  }, [update.timestamp]);

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-md"
    >
      <div className="flex items-center gap-2">
        {getUpdateTypeIcon(update.type)}
        <Badge variant="outline" className={getUpdateTypeColor(update.type)}>
          {update.type.replace('_', ' ')}
        </Badge>
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm truncate">
          {update.message || `${update.type} update`}
        </p>
        {update.nodeId && (
          <p className="text-xs text-gray-500">
            Node: {update.nodeId}
          </p>
        )}
      </div>
      
      <span className="text-xs text-gray-400 whitespace-nowrap">
        {timeAgo}
      </span>
    </motion.div>
  );
};

const ConnectionStatus: React.FC<{ connection: RealTimeConnection }> = ({ connection }) => {
  const getStatusColor = () => {
    switch (connection.status) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
        return 'text-yellow-600';
      case 'disconnected':
        return 'text-gray-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    const iconProps = { className: cn("h-4 w-4", getStatusColor()) };
    
    switch (connection.status) {
      case 'connected':
        return <Wifi {...iconProps} />;
      case 'connecting':
        return <Activity {...iconProps} className="animate-pulse" />;
      case 'disconnected':
      case 'error':
        return <WifiOff {...iconProps} />;
      default:
        return <WifiOff {...iconProps} />;
    }
  };

  return (
    <div className="flex items-center gap-2">
      {getStatusIcon()}
      <span className={cn("text-sm font-medium", getStatusColor())}>
        {connection.status}
      </span>
      {connection.latency && (
        <span className="text-xs text-gray-500">
          ({connection.latency}ms)
        </span>
      )}
    </div>
  );
};

export const RealTimeUpdates: React.FC<RealTimeUpdatesProps> = ({
  threadId,
  onUpdate,
  onConnectionChange,
  className,
  autoConnect = true,
  showNotifications = true
}) => {
  const [connection, setConnection] = useState<RealTimeConnection>({
    status: 'disconnected',
    updateCount: 0
  });
  const [updates, setUpdates] = useState<RealTimeUpdate[]>([]);
  const [isEnabled, setIsEnabled] = useState(autoConnect);
  const [isPaused, setIsPaused] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(showNotifications);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Simulate WebSocket connection (replace with actual WebSocket implementation)
  const connect = useCallback(() => {
    if (!isEnabled || connection.status === 'connected' || connection.status === 'connecting') {
      return;
    }

    setConnection(prev => ({ ...prev, status: 'connecting' }));

    // Simulate connection delay
    setTimeout(() => {
      setConnection(prev => ({ 
        ...prev, 
        status: 'connected',
        lastUpdate: new Date(),
        latency: Math.floor(Math.random() * 50) + 10
      }));

      // Simulate periodic updates
      const interval = setInterval(() => {
        if (isPaused) return;

        const updateTypes: RealTimeUpdate['type'][] = [
          'state_change', 'node_transition', 'interrupt', 'completion'
        ];
        
        const randomType = updateTypes[Math.floor(Math.random() * updateTypes.length)];
        const update: RealTimeUpdate = {
          id: Math.random().toString(36).substr(2, 9),
          type: randomType,
          timestamp: new Date(),
          threadId,
          nodeId: `node_${Math.floor(Math.random() * 5) + 1}`,
          message: `${randomType.replace('_', ' ')} occurred`,
          data: { example: 'data' }
        };

        setUpdates(prev => [update, ...prev.slice(0, 49)]); // Keep last 50 updates
        setConnection(prev => ({ 
          ...prev, 
          updateCount: prev.updateCount + 1,
          lastUpdate: new Date()
        }));

        if (onUpdate) {
          onUpdate(update);
        }

        if (notificationsEnabled && update.type === 'interrupt') {
          toast.info('New interrupt received', {
            description: update.message
          });
        }
      }, 3000 + Math.random() * 2000); // Random interval between 3-5 seconds

      pingIntervalRef.current = interval;
    }, 1000);
  }, [isEnabled, connection.status, threadId, onUpdate, isPaused, notificationsEnabled]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setConnection(prev => ({ ...prev, status: 'disconnected' }));
  }, []);

  const toggleConnection = useCallback(() => {
    if (connection.status === 'connected') {
      disconnect();
    } else {
      connect();
    }
  }, [connection.status, connect, disconnect]);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
    setConnection(prev => ({ ...prev, updateCount: 0 }));
  }, []);

  useEffect(() => {
    if (onConnectionChange) {
      onConnectionChange(connection);
    }
  }, [connection, onConnectionChange]);

  useEffect(() => {
    if (isEnabled && autoConnect) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [isEnabled, autoConnect, connect, disconnect]);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Real-time Updates
            {connection.updateCount > 0 && (
              <Badge variant="secondary">
                {connection.updateCount}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <ConnectionStatus connection={connection} />
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Settings</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setIsEnabled(!isEnabled)}>
                  {isEnabled ? 'Disable' : 'Enable'} Updates
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setIsPaused(!isPaused)}>
                  {isPaused ? 'Resume' : 'Pause'} Updates
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setNotificationsEnabled(!notificationsEnabled)}>
                  {notificationsEnabled ? 'Disable' : 'Enable'} Notifications
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={clearUpdates}>
                  Clear History
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <span>Auto-connect:</span>
            <Switch
              checked={isEnabled}
              onCheckedChange={setIsEnabled}
              size="sm"
            />
          </div>
          
          {connection.status === 'connected' && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPaused(!isPaused)}
                className="h-6 px-2"
              >
                {isPaused ? <Play className="h-3 w-3" /> : <Pause className="h-3 w-3" />}
                {isPaused ? 'Resume' : 'Pause'}
              </Button>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotificationsEnabled(!notificationsEnabled)}
              className="h-6 px-2"
            >
              {notificationsEnabled ? <Bell className="h-3 w-3" /> : <BellOff className="h-3 w-3" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-1 max-h-64 overflow-y-auto">
          <AnimatePresence>
            {updates.length > 0 ? (
              updates.map(update => (
                <UpdateItem key={update.id} update={update} />
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                <Activity className="h-6 w-6 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No updates yet</p>
                {connection.status === 'disconnected' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleConnection}
                    className="mt-2"
                  >
                    Connect
                  </Button>
                )}
              </div>
            )}
          </AnimatePresence>
        </div>
        
        {connection.lastUpdate && (
          <div className="mt-3 pt-3 border-t text-xs text-gray-500">
            Last update: {connection.lastUpdate.toLocaleTimeString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
