import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  GitBranch, 
  Settings, 
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  RefreshCw
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  EnhancedThreadData, 
  WorkflowGraph, 
  StateModification,
  HumanResponse
} from '../types';
import { WorkflowDiagram } from './workflow-diagram';
import { InteractiveStateEditor } from './interactive-state-editor';
import { EnhancedInterruptManager } from './enhanced-interrupt-manager';
import { RealTimeUpdates } from './real-time-updates';
import { ThreadActionsView } from './thread-actions-view';
import { StateView } from './state-view';

interface EnhancedThreadViewProps {
  threadData: EnhancedThreadData;
  onBack: () => void;
  onStateModification?: (modifications: StateModification[]) => void;
  onInterruptAction?: (interruptId: string, action: HumanResponse) => void;
  onWorkflowNodeClick?: (nodeId: string) => void;
  className?: string;
}

const createMockWorkflowGraph = (threadData: EnhancedThreadData): WorkflowGraph => {
  // Create a mock workflow graph based on thread data
  // In a real implementation, this would come from the LangGraph execution
  return {
    nodes: [
      {
        id: 'start',
        name: 'Start',
        type: 'start',
        status: 'completed',
        position: { x: 50, y: 50 }
      },
      {
        id: 'process_1',
        name: 'Process Input',
        type: 'process',
        status: 'completed',
        position: { x: 200, y: 50 }
      },
      {
        id: 'decision_1',
        name: 'Need Human Input?',
        type: 'decision',
        status: threadData.status === 'interrupted' ? 'active' : 'completed',
        position: { x: 350, y: 50 }
      },
      {
        id: 'human_input',
        name: 'Human Input',
        type: 'human',
        status: threadData.status === 'interrupted' ? 'interrupted' : 'pending',
        position: { x: 350, y: 150 }
      },
      {
        id: 'process_2',
        name: 'Process Response',
        type: 'process',
        status: 'pending',
        position: { x: 500, y: 50 }
      },
      {
        id: 'end',
        name: 'End',
        type: 'end',
        status: 'pending',
        position: { x: 650, y: 50 }
      }
    ],
    edges: [
      { id: 'e1', source: 'start', target: 'process_1', label: 'begin' },
      { id: 'e2', source: 'process_1', target: 'decision_1' },
      { id: 'e3', source: 'decision_1', target: 'human_input', label: 'yes' },
      { id: 'e4', source: 'decision_1', target: 'process_2', label: 'no' },
      { id: 'e5', source: 'human_input', target: 'process_2' },
      { id: 'e6', source: 'process_2', target: 'end' }
    ],
    currentNode: threadData.status === 'interrupted' ? 'human_input' : 'decision_1',
    executionPath: ['start', 'process_1', 'decision_1']
  };
};

export const EnhancedThreadView: React.FC<EnhancedThreadViewProps> = ({
  threadData,
  onBack,
  onStateModification,
  onInterruptAction,
  onWorkflowNodeClick,
  className
}) => {
  const [activeTab, setActiveTab] = useState('workflow');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showRealTime, setShowRealTime] = useState(true);
  const [workflowGraph, setWorkflowGraph] = useState<WorkflowGraph>(() => 
    threadData.workflowGraph || createMockWorkflowGraph(threadData)
  );

  const isInterrupted = threadData.status === 'interrupted';
  const hasInterrupts = threadData.interrupts && threadData.interrupts.length > 0;

  // Convert regular interrupts to enhanced interrupts for the manager
  const enhancedInterrupts = useMemo(() => {
    if (!threadData.interrupts) return [];
    
    return threadData.interrupts.map((interrupt, index) => ({
      ...interrupt,
      id: `interrupt_${index}`,
      priority: 'medium' as const,
      category: 'input' as const,
      timestamp: new Date(),
      status: 'pending' as const
    }));
  }, [threadData.interrupts]);

  const handleStateModification = useCallback((modifications: StateModification[]) => {
    if (onStateModification) {
      onStateModification(modifications);
    }
  }, [onStateModification]);

  const handleInterruptAction = useCallback((interruptId: string, action: HumanResponse) => {
    if (onInterruptAction) {
      onInterruptAction(interruptId, action);
    }
  }, [onInterruptAction]);

  const handleWorkflowNodeClick = useCallback((nodeId: string) => {
    if (onWorkflowNodeClick) {
      onWorkflowNodeClick(nodeId);
    }
    
    // Update workflow graph to highlight selected node
    setWorkflowGraph(prev => ({
      ...prev,
      currentNode: nodeId
    }));
  }, [onWorkflowNodeClick]);

  const handleRealTimeUpdate = useCallback((update: any) => {
    // Handle real-time updates to workflow graph
    if (update.type === 'node_transition' && update.nodeId) {
      setWorkflowGraph(prev => ({
        ...prev,
        currentNode: update.nodeId,
        executionPath: [...prev.executionPath, update.nodeId]
      }));
    }
  }, []);

  const threadTitle = useMemo(() => {
    if (threadData.interrupts?.[0]?.action_request?.action) {
      return threadData.interrupts[0].action_request.action;
    }
    return `Thread: ${threadData.thread.thread_id.slice(0, 8)}...`;
  }, [threadData]);

  return (
    <div className={cn(
      "flex flex-col h-full w-full",
      isFullscreen && "fixed inset-0 z-50 bg-white",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="h-8 w-8 p-0"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-gray-600" />
            <h1 className="text-lg font-semibold">{threadTitle}</h1>
            <Badge variant={isInterrupted ? "destructive" : "secondary"}>
              {threadData.status}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRealTime(!showRealTime)}
          >
            {showRealTime ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Tabs */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
              <TabsTrigger value="workflow">Workflow</TabsTrigger>
              <TabsTrigger value="state">State</TabsTrigger>
              <TabsTrigger value="interrupts" className="relative">
                Interrupts
                {hasInterrupts && (
                  <Badge className="ml-1 h-4 w-4 p-0 text-xs">
                    {threadData.interrupts?.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 p-4 overflow-auto">
              <TabsContent value="workflow" className="mt-0 h-full">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <GitBranch className="h-4 w-4" />
                      Workflow Visualization
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="h-full">
                    <WorkflowDiagram
                      graph={workflowGraph}
                      onNodeClick={handleWorkflowNodeClick}
                      className="h-full"
                    />
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="state" className="mt-0 h-full">
                <InteractiveStateEditor
                  state={threadData.thread.values || {}}
                  onStateChange={handleStateModification}
                  readOnly={false}
                  showDiff={true}
                  className="h-full"
                />
              </TabsContent>
              
              <TabsContent value="interrupts" className="mt-0 h-full">
                {hasInterrupts ? (
                  <EnhancedInterruptManager
                    interrupts={enhancedInterrupts}
                    onInterruptAction={handleInterruptAction}
                    showFilters={true}
                    className="h-full"
                  />
                ) : (
                  <Card className="h-full">
                    <CardContent className="flex items-center justify-center h-full">
                      <div className="text-center text-gray-500">
                        <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No interrupts for this thread</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
              
              <TabsContent value="actions" className="mt-0 h-full">
                <div className="h-full overflow-auto">
                  <ThreadActionsView
                    threadData={threadData}
                    isInterrupted={isInterrupted}
                    threadTitle={threadTitle}
                    showState={false}
                    showDescription={false}
                    setThreadData={() => {}} // Handle this properly in real implementation
                  />
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Right Panel - Real-time Updates */}
        <AnimatePresence>
          {showRealTime && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 320, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              className="border-l bg-gray-50 overflow-hidden"
            >
              <div className="p-4 h-full">
                <RealTimeUpdates
                  threadId={threadData.thread.thread_id}
                  onUpdate={handleRealTimeUpdate}
                  autoConnect={true}
                  showNotifications={true}
                  className="h-full"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
