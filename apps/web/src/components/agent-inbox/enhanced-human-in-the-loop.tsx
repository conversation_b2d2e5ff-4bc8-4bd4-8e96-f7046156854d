import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  Settings,
  Users,
  Activity,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { 
  EnhancedThreadData, 
  WorkflowGraph, 
  StateModification,
  HumanResponse,
  ThreadData
} from './types';
import { EnhancedThreadView } from './components/enhanced-thread-view';
import { WorkflowDiagram } from './components/workflow-diagram';
import { InteractiveStateEditor } from './components/interactive-state-editor';
import { EnhancedInterruptManager } from './components/enhanced-interrupt-manager';
import { RealTimeUpdates } from './components/real-time-updates';

interface EnhancedHumanInTheLoopProps {
  threadData: ThreadData;
  onThreadUpdate?: (threadData: ThreadData) => void;
  onWorkflowAction?: (action: 'play' | 'pause' | 'stop' | 'restart') => void;
  className?: string;
}

const convertToEnhancedThreadData = (threadData: ThreadData): EnhancedThreadData => {
  return {
    ...threadData,
    workflowGraph: undefined, // Will be generated in EnhancedThreadView
    stateModifications: [],
    realTimeUpdates: true
  };
};

export const EnhancedHumanInTheLoop: React.FC<EnhancedHumanInTheLoopProps> = ({
  threadData,
  onThreadUpdate,
  onWorkflowAction,
  className
}) => {
  const [enhancedThreadData, setEnhancedThreadData] = useState<EnhancedThreadData>(() =>
    convertToEnhancedThreadData(threadData)
  );
  const [workflowState, setWorkflowState] = useState<'idle' | 'running' | 'paused' | 'error'>('idle');
  const [showEnhancedView, setShowEnhancedView] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Update enhanced thread data when threadData changes
  useEffect(() => {
    setEnhancedThreadData(convertToEnhancedThreadData(threadData));
  }, [threadData]);

  const handleWorkflowAction = useCallback((action: 'play' | 'pause' | 'stop' | 'restart') => {
    if (onWorkflowAction) {
      onWorkflowAction(action);
    }

    switch (action) {
      case 'play':
        setWorkflowState('running');
        toast.success('Workflow resumed');
        break;
      case 'pause':
        setWorkflowState('paused');
        toast.info('Workflow paused');
        break;
      case 'stop':
        setWorkflowState('idle');
        toast.info('Workflow stopped');
        break;
      case 'restart':
        setWorkflowState('running');
        toast.success('Workflow restarted');
        break;
    }
  }, [onWorkflowAction]);

  const handleStateModification = useCallback((modifications: StateModification[]) => {
    setEnhancedThreadData(prev => ({
      ...prev,
      stateModifications: modifications
    }));

    toast.success('State modifications applied', {
      description: `${modifications.length} change(s) made`
    });
  }, []);

  const handleInterruptAction = useCallback((interruptId: string, action: HumanResponse) => {
    // Handle interrupt action
    toast.success('Interrupt action processed', {
      description: `Action: ${action.type}`
    });

    // Update thread data if callback provided
    if (onThreadUpdate) {
      const updatedThreadData = { ...threadData };
      // Apply interrupt resolution logic here
      onThreadUpdate(updatedThreadData);
    }
  }, [threadData, onThreadUpdate]);

  const handleWorkflowNodeClick = useCallback((nodeId: string) => {
    toast.info('Node selected', {
      description: `Selected node: ${nodeId}`
    });
  }, []);

  const getWorkflowStateColor = () => {
    switch (workflowState) {
      case 'running':
        return 'text-green-600';
      case 'paused':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getWorkflowStateIcon = () => {
    const iconProps = { className: cn("h-4 w-4", getWorkflowStateColor()) };
    
    switch (workflowState) {
      case 'running':
        return <Play {...iconProps} />;
      case 'paused':
        return <Pause {...iconProps} />;
      case 'error':
        return <AlertTriangle {...iconProps} />;
      default:
        return <Square {...iconProps} />;
    }
  };

  const isInterrupted = threadData.status === 'interrupted';
  const hasInterrupts = threadData.interrupts && threadData.interrupts.length > 0;

  if (showEnhancedView) {
    return (
      <EnhancedThreadView
        threadData={enhancedThreadData}
        onBack={() => setShowEnhancedView(false)}
        onStateModification={handleStateModification}
        onInterruptAction={handleInterruptAction}
        onWorkflowNodeClick={handleWorkflowNodeClick}
        className={className}
      />
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Human-in-the-Loop Control
              </CardTitle>
              <div className="flex items-center gap-2">
                {getWorkflowStateIcon()}
                <span className={cn("text-sm font-medium", getWorkflowStateColor())}>
                  {workflowState}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEnhancedView(true)}
              >
                <Settings className="h-4 w-4 mr-1" />
                Enhanced View
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Thread:</span>
                <Badge variant="outline">
                  {threadData.thread.thread_id.slice(0, 8)}...
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={isInterrupted ? "destructive" : "secondary"}>
                  {threadData.status}
                </Badge>
              </div>
              
              {hasInterrupts && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Interrupts:</span>
                  <Badge variant="destructive">
                    {threadData.interrupts?.length}
                  </Badge>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWorkflowAction('play')}
                disabled={workflowState === 'running'}
              >
                <Play className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWorkflowAction('pause')}
                disabled={workflowState !== 'running'}
              >
                <Pause className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWorkflowAction('stop')}
                disabled={workflowState === 'idle'}
              >
                <Square className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWorkflowAction('restart')}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workflow">Workflow</TabsTrigger>
          <TabsTrigger value="state">State</TabsTrigger>
          <TabsTrigger value="interrupts" className="relative">
            Interrupts
            {hasInterrupts && (
              <Badge className="ml-1 h-4 w-4 p-0 text-xs">
                {threadData.interrupts?.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Thread Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">ID:</span>
                    <span className="ml-2 font-mono text-xs">
                      {threadData.thread.thread_id}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Status:</span>
                    <span className="ml-2 capitalize">{threadData.status}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Created:</span>
                    <span className="ml-2">
                      {new Date(threadData.thread.created_at).toLocaleString()}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Updated:</span>
                    <span className="ml-2">
                      {new Date(threadData.thread.updated_at).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <RealTimeUpdates
              threadId={threadData.thread.thread_id}
              autoConnect={true}
              showNotifications={false}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="workflow">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Visualization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                <WorkflowDiagram
                  graph={enhancedThreadData.workflowGraph || { nodes: [], edges: [], executionPath: [] }}
                  onNodeClick={handleWorkflowNodeClick}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="state">
          <InteractiveStateEditor
            state={threadData.thread.values || {}}
            onStateChange={handleStateModification}
            readOnly={false}
            showDiff={true}
          />
        </TabsContent>
        
        <TabsContent value="interrupts">
          {hasInterrupts ? (
            <EnhancedInterruptManager
              interrupts={enhancedThreadData.interrupts?.map((interrupt, index) => ({
                ...interrupt,
                id: `interrupt_${index}`,
                priority: 'medium' as const,
                category: 'input' as const,
                timestamp: new Date(),
                status: 'pending' as const
              })) || []}
              onInterruptAction={handleInterruptAction}
              showFilters={true}
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-32">
                <div className="text-center text-gray-500">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No interrupts for this thread</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
