import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  AlertCircle,
  CheckCircle,
  Clock,
  User
} from 'lucide-react';
import { EnhancedHumanInTheLoop } from '../enhanced-human-in-the-loop';
import { WorkflowDiagram } from '../components/workflow-diagram';
import { InteractiveStateEditor } from '../components/interactive-state-editor';
import { EnhancedInterruptManager } from '../components/enhanced-interrupt-manager';
import { RealTimeUpdates } from '../components/real-time-updates';
import { 
  ThreadData, 
  WorkflowGraph, 
  StateModification,
  HumanResponse
} from '../types';

// Mock data for demonstration
const createMockThreadData = (): ThreadData => ({
  thread: {
    thread_id: 'demo-thread-12345',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    values: {
      user_input: 'Hello, I need help with my order',
      current_step: 'awaiting_approval',
      order_details: {
        id: 'ORD-001',
        amount: 299.99,
        status: 'pending'
      },
      messages: [
        { role: 'user', content: 'Hello, I need help with my order' },
        { role: 'assistant', content: 'I can help you with that. Let me check your order details.' }
      ]
    }
  },
  status: 'interrupted',
  interrupts: [
    {
      action_request: {
        action: 'approve_refund',
        args: {
          order_id: 'ORD-001',
          amount: 299.99,
          reason: 'Customer requested refund due to delayed delivery'
        }
      },
      config: {
        allow_ignore: true,
        allow_respond: true,
        allow_edit: true,
        allow_accept: true
      },
      description: 'Customer is requesting a refund for order ORD-001 due to delayed delivery. Please review and approve or deny the refund request.'
    }
  ]
});

const createMockWorkflowGraph = (): WorkflowGraph => ({
  nodes: [
    {
      id: 'start',
      name: 'Receive Request',
      type: 'start',
      status: 'completed',
      position: { x: 50, y: 100 },
      metadata: { description: 'Customer submits support request' }
    },
    {
      id: 'analyze',
      name: 'Analyze Request',
      type: 'process',
      status: 'completed',
      position: { x: 200, y: 100 },
      metadata: { description: 'AI analyzes the customer request' }
    },
    {
      id: 'decision',
      name: 'Needs Approval?',
      type: 'decision',
      status: 'completed',
      position: { x: 350, y: 100 },
      metadata: { description: 'Determine if human approval is needed' }
    },
    {
      id: 'human_approval',
      name: 'Human Approval',
      type: 'human',
      status: 'interrupted',
      position: { x: 350, y: 200 },
      metadata: { description: 'Waiting for human to approve/deny' }
    },
    {
      id: 'auto_resolve',
      name: 'Auto Resolve',
      type: 'process',
      status: 'pending',
      position: { x: 500, y: 100 },
      metadata: { description: 'Automatically resolve simple requests' }
    },
    {
      id: 'process_approval',
      name: 'Process Decision',
      type: 'process',
      status: 'pending',
      position: { x: 500, y: 200 },
      metadata: { description: 'Process the approval decision' }
    },
    {
      id: 'end',
      name: 'Complete',
      type: 'end',
      status: 'pending',
      position: { x: 650, y: 150 },
      metadata: { description: 'Request completed' }
    }
  ],
  edges: [
    { id: 'e1', source: 'start', target: 'analyze', label: 'submit' },
    { id: 'e2', source: 'analyze', target: 'decision' },
    { id: 'e3', source: 'decision', target: 'human_approval', label: 'yes' },
    { id: 'e4', source: 'decision', target: 'auto_resolve', label: 'no' },
    { id: 'e5', source: 'human_approval', target: 'process_approval' },
    { id: 'e6', source: 'auto_resolve', target: 'end' },
    { id: 'e7', source: 'process_approval', target: 'end' }
  ],
  currentNode: 'human_approval',
  executionPath: ['start', 'analyze', 'decision', 'human_approval']
});

export const EnhancedHITLDemo: React.FC = () => {
  const [threadData, setThreadData] = useState<ThreadData>(createMockThreadData);
  const [workflowGraph, setWorkflowGraph] = useState<WorkflowGraph>(createMockWorkflowGraph);
  const [activeDemo, setActiveDemo] = useState<string>('overview');

  const handleThreadUpdate = (updatedThreadData: ThreadData) => {
    setThreadData(updatedThreadData);
  };

  const handleWorkflowAction = (action: 'play' | 'pause' | 'stop' | 'restart') => {
    console.log('Demo workflow action:', action);
    
    // Simulate workflow state changes
    if (action === 'play') {
      setWorkflowGraph(prev => ({
        ...prev,
        nodes: prev.nodes.map(node => 
          node.id === 'human_approval' 
            ? { ...node, status: 'active' }
            : node
        )
      }));
    }
  };

  const handleStateModification = (modifications: StateModification[]) => {
    console.log('Demo state modifications:', modifications);
    // In a real implementation, these would be sent to the LangGraph backend
  };

  const handleInterruptAction = (interruptId: string, action: HumanResponse) => {
    console.log('Demo interrupt action:', { interruptId, action });
    
    // Simulate resolving the interrupt
    setThreadData(prev => ({
      ...prev,
      status: 'idle',
      interrupts: []
    }));
    
    // Update workflow to show completion
    setWorkflowGraph(prev => ({
      ...prev,
      currentNode: 'process_approval',
      nodes: prev.nodes.map(node => {
        if (node.id === 'human_approval') {
          return { ...node, status: 'completed' };
        }
        if (node.id === 'process_approval') {
          return { ...node, status: 'active' };
        }
        return node;
      }),
      executionPath: [...prev.executionPath, 'process_approval']
    }));
  };

  const handleWorkflowNodeClick = (nodeId: string) => {
    console.log('Demo node clicked:', nodeId);
    setWorkflowGraph(prev => ({
      ...prev,
      currentNode: nodeId
    }));
  };

  const enhancedInterrupts = threadData.interrupts?.map((interrupt, index) => ({
    ...interrupt,
    id: `demo_interrupt_${index}`,
    priority: 'high' as const,
    category: 'approval' as const,
    timestamp: new Date(),
    status: 'pending' as const,
    estimatedTime: 5,
    assignedTo: 'Support Team'
  })) || [];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Enhanced Human-in-the-Loop Demo</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          This demo showcases the enhanced visual human-in-the-loop system for LangGraph workflows. 
          Explore interactive workflow diagrams, state editing, interrupt management, and real-time updates.
        </p>
      </div>

      {/* Demo Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Demo Scenario: Customer Refund Request
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-blue-600" />
              <span className="text-sm">Customer: John Doe</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="text-sm">Status: Awaiting Approval</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm">Amount: $299.99</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demo Tabs */}
      <Tabs value={activeDemo} onValueChange={setActiveDemo}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workflow">Workflow</TabsTrigger>
          <TabsTrigger value="state">State Editor</TabsTrigger>
          <TabsTrigger value="interrupts">Interrupts</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <EnhancedHumanInTheLoop
            threadData={threadData}
            onThreadUpdate={handleThreadUpdate}
            onWorkflowAction={handleWorkflowAction}
          />
        </TabsContent>

        <TabsContent value="workflow">
          <Card>
            <CardHeader>
              <CardTitle>Interactive Workflow Diagram</CardTitle>
              <p className="text-sm text-gray-600">
                Click on nodes to inspect them. The current execution is paused at the "Human Approval" step.
              </p>
            </CardHeader>
            <CardContent>
              <div className="h-96 border rounded-lg">
                <WorkflowDiagram
                  graph={workflowGraph}
                  onNodeClick={handleWorkflowNodeClick}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="state">
          <Card>
            <CardHeader>
              <CardTitle>Interactive State Editor</CardTitle>
              <p className="text-sm text-gray-600">
                Edit the workflow state directly. Changes are tracked and can be applied to the running workflow.
              </p>
            </CardHeader>
            <CardContent>
              <InteractiveStateEditor
                state={threadData.thread.values || {}}
                onStateChange={handleStateModification}
                readOnly={false}
                showDiff={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="interrupts">
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Interrupt Management</CardTitle>
              <p className="text-sm text-gray-600">
                Manage interrupts with priority levels, categories, and advanced filtering options.
              </p>
            </CardHeader>
            <CardContent>
              <EnhancedInterruptManager
                interrupts={enhancedInterrupts}
                onInterruptAction={handleInterruptAction}
                showFilters={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Updates</CardTitle>
              <p className="text-sm text-gray-600">
                Monitor workflow execution in real-time with live updates and notifications.
              </p>
            </CardHeader>
            <CardContent>
              <RealTimeUpdates
                threadId={threadData.thread.thread_id}
                autoConnect={true}
                showNotifications={true}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Demo Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Demo Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={() => handleWorkflowAction('play')}
            >
              <Play className="h-4 w-4 mr-1" />
              Resume Workflow
            </Button>
            <Button
              variant="outline"
              onClick={() => handleWorkflowAction('pause')}
            >
              <Pause className="h-4 w-4 mr-1" />
              Pause Workflow
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setThreadData(createMockThreadData());
                setWorkflowGraph(createMockWorkflowGraph());
              }}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset Demo
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
