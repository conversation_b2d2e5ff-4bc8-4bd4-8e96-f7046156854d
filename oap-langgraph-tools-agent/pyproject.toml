[project]
name = "tools_agent"
version = "0.1.0"
description = "LangGraph tools agent with MCP and a RAG tool"
authors = [
    { name = "langchain-ai" }, 
]
requires-python = ">=3.11.0,<3.13"
dependencies = [
    "langgraph==0.4.3",
    "langchain-anthropic==0.3.13",
    "langchain-core==0.3.59",
    "langchain-openai==0.3.16",
    "pydantic==2.11.3",
    "langchain==0.3.25",
    "mcp==1.9.1",
    "supabase>=2.15.1",
    "aiohttp>=3.8.0",
]

[tool.setuptools]
packages = ["tools_agent"]

[dependency-groups]
dev = [
    "ruff>=0.8.4",
    "langgraph-api==0.2.27",
    "langgraph-cli==0.2.10",
    "langgraph-runtime-inmem>=0.0.11",
]